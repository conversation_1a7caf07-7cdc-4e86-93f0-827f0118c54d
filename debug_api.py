#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API问题
"""

import requests
import json

def test_simple_request():
    """测试简单的API请求"""
    print("🔍 测试简单的API请求...")
    
    try:
        # 最简单的测试数据
        test_data = {
            "urls": [
                {
                    "filename": "B07XXXXX_MAIN.jpg",
                    "url": "https://example.com/test.jpg"
                }
            ]
        }
        
        print(f"📤 发送数据: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(
            'http://localhost:5000/api/parse-urls',
            headers={'Content-Type': 'application/json'},
            json=test_data,
            timeout=10
        )
        
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📝 响应头: {dict(response.headers)}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功: {result}")
        else:
            print(f"❌ 失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")

if __name__ == "__main__":
    test_simple_request()
